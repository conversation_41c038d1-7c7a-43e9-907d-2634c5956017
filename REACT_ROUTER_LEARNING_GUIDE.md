# React Router v6 Learning Guide

## 🎯 Overview
This project demonstrates all the key concepts of React Router v6 for building single-page applications (SPAs) with client-side routing.

## 🔑 Key Concepts Explained

### 1. **Router Setup (App.js)**
```javascript
// The foundation - these imports give us routing capabilities
import { RouterProvider, createBrowserRouter, createRoutesFromElements, Route } from 'react-router-dom';

// Create the router configuration
const appRouter = createBrowserRouter(
  createRoutesFromElements(
    // Route definitions go here
  )
);

// Provide routing to the entire app
<RouterProvider router={appRouter} />
```

**What's happening:**
- `createBrowserRouter`: Creates a router that uses clean URLs (no # symbols)
- `createRoutesFromElements`: Converts JSX Route elements into route objects
- `RouterProvider`: Makes routing available throughout your app

### 2. **Route Types**

#### **Index Route**
```javascript
<Route index element={<HomePage />} />
```
- Renders when the path exactly matches the parent route
- For parent path "/", index route renders at "/"

#### **Static Routes**
```javascript
<Route path="search" element={<SearchPage />} />
```
- Matches exact path: "/search"

#### **Dynamic Routes with Parameters**
```javascript
<Route path=":type" element={<HomePage />} />           // Matches "/dogs", "/cats"
<Route path=":type/:id" element={<PetDetailsPage />} /> // Matches "/dogs/123"
```
- `:type` and `:id` are URL parameters
- Access them with `useParams()` hook

### 3. **Nested Routes & Outlet**
```javascript
// Parent route
<Route path="/" element={<Root />}>
  {/* Child routes render inside the Outlet */}
  <Route index element={<HomePage />} />
  <Route path=":type" element={<HomePage />} />
</Route>

// In Root component
<Navigation />
<Outlet /> {/* Child routes render here */}
```

**Benefits:**
- Shared layout (navigation stays visible)
- Only content area changes
- Cleaner code organization

### 4. **Navigation Components**

#### **Link vs NavLink**
```javascript
// Link - basic navigation
<Link to="/dogs">Dogs</Link>

// NavLink - navigation with active states
<NavLink 
  to="/dogs"
  className={({ isActive }) => isActive ? 'active' : 'normal'}
>
  Dogs
</NavLink>
```

**Key differences:**
- `Link`: Basic navigation, no active state
- `NavLink`: Knows when it's active, perfect for navigation menus

### 5. **Hooks for Route Data**

#### **useParams() - URL Parameters**
```javascript
// For route: "/dogs/123"
const { type, id } = useParams();
// type = "dogs", id = "123"
```

#### **useSearchParams() - Query Parameters**
```javascript
// For URL: "/search?name=fluffy&age=2"
const [searchParams] = useSearchParams();
const name = searchParams.get('name'); // "fluffy"
const age = searchParams.get('age');   // "2"
```

#### **useNavigate() - Programmatic Navigation**
```javascript
const navigate = useNavigate();

// Navigate to a route
navigate('/dogs');

// Navigate with query parameters
navigate('/search?name=fluffy');

// Go back in history
navigate(-1);
```

### 6. **Search Parameters & Query Strings**

#### **Creating Search Parameters**
```javascript
import { createSearchParams } from 'react-router-dom';

const searchQuery = { name: 'fluffy', age: '2' };
const query = createSearchParams(searchQuery);
navigate(`/search?${query}`); // "/search?name=fluffy&age=2"
```

#### **Reading Search Parameters**
```javascript
const [searchParams] = useSearchParams();
const petName = searchParams.get('name');
```

### 7. **Error Handling & Redirects**

#### **Declarative Redirects**
```javascript
// Redirect when component renders
{error ? <Navigate to="/error-page" /> : <div>Content</div>}
```

#### **Imperative Redirects**
```javascript
// Redirect in response to user action
const handleError = () => {
  navigate('/error-page');
};
```

## 🔄 Data Flow Examples

### **Navigation Flow:**
1. User clicks NavLink → URL changes → Route matches → Component renders
2. Component uses useParams() → Extracts URL data → Fetches data → Updates UI

### **Search Flow:**
1. User types in search → Form submits → useNavigate() changes URL
2. URL includes query parameters → SearchPage renders → useSearchParams() extracts query
3. Component fetches filtered data → Displays results

## 🎨 Best Practices

1. **Use Link/NavLink for internal navigation** (not `<a>` tags)
2. **Use useParams() for URL parameters** (like IDs, categories)
3. **Use useSearchParams() for filters and search** (optional data)
4. **Use Navigate component for conditional redirects**
5. **Use useNavigate() for programmatic navigation** (after form submissions, etc.)

## 🚀 Testing Your Understanding

Try these exercises:
1. Add a new route for "/favorites"
2. Create a route with multiple parameters: "/pets/:type/:id/:action"
3. Add search filters using multiple query parameters
4. Implement breadcrumb navigation using current route data

## 📚 Route Structure Summary

```
/ (Root - always renders Navigation + Outlet)
├── index (HomePage - all pets)
├── :type (HomePage - filtered by type)
├── :type/:id (PetDetailsPage - specific pet)
├── search (SearchPage - search results)
└── pet-details-not-found (Error page)
```

This structure allows for clean URLs, shared layouts, and intuitive navigation patterns!
