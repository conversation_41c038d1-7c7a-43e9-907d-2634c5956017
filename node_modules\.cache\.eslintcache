[{"C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\mocks\\browser.js": "4", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\mocks\\handlers.js": "5", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\detail\\index.js": "6", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\home\\index.js": "7", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\search\\index.js": "8", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\petDetailsNotFound\\index.js": "9", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\root\\index.js": "10", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\hero\\index.js": "11", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\api\\petfinder\\index.js": "12", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\pet\\index.js": "13", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\navigation\\index.js": "14", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\search\\index.js": "15"}, {"size": 604, "mtime": 1750623426394, "results": "16", "hashOfConfig": "17"}, {"size": 364, "mtime": 1750623426378, "results": "18", "hashOfConfig": "17"}, {"size": 2522, "mtime": 1750624768946, "results": "19", "hashOfConfig": "17"}, {"size": 123, "mtime": 1750623426461, "results": "20", "hashOfConfig": "17"}, {"size": 1171, "mtime": 1750623426455, "results": "21", "hashOfConfig": "17"}, {"size": 2598, "mtime": 1750624890626, "results": "22", "hashOfConfig": "17"}, {"size": 2735, "mtime": 1750624852589, "results": "23", "hashOfConfig": "17"}, {"size": 1711, "mtime": 1750624932441, "results": "24", "hashOfConfig": "17"}, {"size": 1124, "mtime": 1750624945753, "results": "25", "hashOfConfig": "17"}, {"size": 909, "mtime": 1750624782730, "results": "26", "hashOfConfig": "17"}, {"size": 769, "mtime": 1750623426560, "results": "27", "hashOfConfig": "17"}, {"size": 719, "mtime": 1750623426590, "results": "28", "hashOfConfig": "17"}, {"size": 705, "mtime": 1750623426530, "results": "29", "hashOfConfig": "17"}, {"size": 2084, "mtime": 1750624811561, "results": "30", "hashOfConfig": "17"}, {"size": 1516, "mtime": 1750624906658, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "35"}, "40cses", {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "35"}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "35"}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "35"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "54", "usedDeprecatedRules": "35"}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "35"}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "35"}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "35"}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "76", "usedDeprecatedRules": "35"}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "35"}, "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\index.js", [], [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\mocks\\browser.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\mocks\\handlers.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\detail\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\home\\index.js", ["80"], [], "import React, { useEffect, useState } from 'react';\nimport { getPets } from '../../api/petfinder';\nimport <PERSON> from '../../components/hero';\n// useParams: Hook that extracts URL parameters from the current route\n// Link: Component for navigation that doesn't cause page refreshes\nimport { usePara<PERSON>, Link } from 'react-router-dom';\n\nconst HomePage = () => {\n  const [data, setData] = useState(null);\n\n  // useParams() returns an object with all URL parameters\n  // For route \":type\", this extracts the type value from URLs like \"/dogs\" or \"/cats\"\n  // If we're on the index route (\"/\"), type will be undefined\n  const { type } = useParams();\n\n  // useEffect runs when the component mounts AND when 'type' changes\n  // This means when user navigates from \"/dogs\" to \"/cats\", this will re-run\n  useEffect(() => {\n    async function getPetsData() {\n      // Pass the type parameter to the API - undefined means \"all pets\"\n      const petsData = await getPets(type);\n      setData(petsData);\n    }\n\n    getPetsData();\n  }, [type]); // Dependency array - effect runs when 'type' changes\n\n  if (!data) {\n    return <h2>Loading...</h2>;\n  }\n\n  return (\n    <div className=\"page\">\n      <Hero />\n      <h3>\n        <span className=\"pet-type-label\">{type ? `${type}s` : 'Pets'}</span>{' '}\n        available for adoption near you\n      </h3>\n\n      {data.length ? (\n        <div className=\"grid\">\n          {data.map((animal) => (\n            {/* Link component replaces <a> tags for internal navigation */}\n            {/* This prevents page refreshes and uses React Router's client-side routing */}\n            <Link\n              key={animal.id}\n              {/* 'to' prop creates URLs like \"/dogs/123\" that match our \":type/:id\" route */}\n              {/* This will navigate to the PetDetailsPage component */}\n              to={`/${animal.type.toLowerCase()}/${animal.id}`}\n              className=\"pet\"\n            >\n              <article>\n                <div className=\"pet-image-container\">\n                  {\n                    <img\n                      className=\"pet-image\"\n                      src={\n                        animal.photos[0]?.medium ||\n                        '/missing-animal.png'\n                      }\n                      alt=\"\"\n                    />\n                  }\n                </div>\n                <h3>{animal.name}</h3>\n                <p>Breed: {animal.breeds.primary}</p>\n                <p>Color: {animal.colors.primary}</p>\n                <p>Gender: {animal.gender}</p>\n              </article>\n            </Link>\n          ))}\n        </div>\n      ) : (\n        <p className=\"prompt\">No {type}s available for adoption now.</p>\n      )}\n    </div>\n  );\n};\n\nexport default HomePage;\n", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\search\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\petDetailsNotFound\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\root\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\hero\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\api\\petfinder\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\pet\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\navigation\\index.js", ["81"], [], "import React, { useEffect, useState } from 'react';\nimport { getPetTypes } from '../../api/petfinder';\nimport Logo from '../../assets/logo.svg';\nimport Search from '../search';\n// NavLink is like Link but with additional features for navigation menus\n// It can automatically apply CSS classes when the link is \"active\" (current page)\nimport { NavLink } from 'react-router-dom';\n\nconst Navigation = () => {\n  const [petTypes, setPetTypes] = useState([]);\n\n  useEffect(() => {\n    async function getPetTypesData() {\n      const { types } = await getPetTypes();\n      setPetTypes(types);\n    }\n\n    getPetTypesData();\n  }, []);\n\n  return (\n    <nav>\n      <div className=\"nav-logo\">\n        <img src={Logo} alt=\"Petlover\" />\n        <Search />\n      </div>\n      <ul className=\"nav-links\">\n        <li key={'all'}>\n          {/* NavLink for \"All Pets\" - goes to the root route \"/\" */}\n          <NavLink\n            to=\"/\"  {/* 'to' prop replaces 'href' - tells React Router where to navigate */}\n            {/* className can be a function that receives navigation state */}\n            {/* isActive is true when this link matches the current URL */}\n            className={({ isActive }) => isActive ? 'nav-link nav-link-active' : 'nav-link'}\n          >\n            All Pets\n          </NavLink>\n        </li>\n        {petTypes\n          ? petTypes.map((type) => (\n              <li key={type.name}>\n                {/* Dynamic NavLinks for each pet type (dogs, cats, etc.) */}\n                <NavLink\n                  {/* This creates URLs like \"/dogs\", \"/cats\" that match our \":type\" route */}\n                  to={`/${type._links.self.href.split('/').pop()}`}\n                  key={type.name}\n                  {/* Same active state logic - highlights the current pet type */}\n                  className={({ isActive }) => isActive ? 'nav-link nav-link-active' : 'nav-link'}\n                >\n                  {type.name}s\n                </NavLink>{' '}\n              </li>\n            ))\n          : 'Loading...'}\n      </ul>\n    </nav>\n  );\n};\n\nexport default Navigation;\n", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\search\\index.js", [], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "82", "line": 44, "column": 12}, {"ruleId": null, "fatal": true, "severity": 2, "message": "83", "line": 31, "column": 91}, "Parsing error: Unexpected token, expected \",\" (44:12)", "Parsing error: Unexpected token, expected \"...\" (31:91)"]