[{"C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\mocks\\browser.js": "4", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\mocks\\handlers.js": "5", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\detail\\index.js": "6", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\home\\index.js": "7", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\search\\index.js": "8", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\petDetailsNotFound\\index.js": "9", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\root\\index.js": "10", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\hero\\index.js": "11", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\api\\petfinder\\index.js": "12", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\pet\\index.js": "13", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\navigation\\index.js": "14", "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\search\\index.js": "15"}, {"size": 604, "mtime": 1750623426394, "results": "16", "hashOfConfig": "17"}, {"size": 364, "mtime": 1750623426378, "results": "18", "hashOfConfig": "17"}, {"size": 964, "mtime": 1750624490371, "results": "19", "hashOfConfig": "17"}, {"size": 123, "mtime": 1750623426461, "results": "20", "hashOfConfig": "17"}, {"size": 1171, "mtime": 1750623426455, "results": "21", "hashOfConfig": "17"}, {"size": 1720, "mtime": 1750624498643, "results": "22", "hashOfConfig": "17"}, {"size": 1773, "mtime": 1750624432250, "results": "23", "hashOfConfig": "17"}, {"size": 1050, "mtime": 1750624472252, "results": "24", "hashOfConfig": "17"}, {"size": 759, "mtime": 1750624510387, "results": "25", "hashOfConfig": "17"}, {"size": 248, "mtime": 1750624351579, "results": "26", "hashOfConfig": "17"}, {"size": 769, "mtime": 1750623426560, "results": "27", "hashOfConfig": "17"}, {"size": 719, "mtime": 1750623426590, "results": "28", "hashOfConfig": "17"}, {"size": 705, "mtime": 1750623426530, "results": "29", "hashOfConfig": "17"}, {"size": 1371, "mtime": 1750624417198, "results": "30", "hashOfConfig": "17"}, {"size": 821, "mtime": 1750624451565, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "40cses", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\mocks\\browser.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\mocks\\handlers.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\detail\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\home\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\search\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\pages\\petDetailsNotFound\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\root\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\hero\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\api\\petfinder\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\pet\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\navigation\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Coding Folders\\Learning Folders\\Current\\CodeCademy Full Stack Path\\Full Stack Path Projects\\React\\Adopt-a-pet\\react_router_v6_project_starting_code\\starting_code\\src\\components\\search\\index.js", [], []]