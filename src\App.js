// Import all the page components that will be rendered by our routes
import HomePage from './pages/home';
import SearchPage from './pages/search';
import PetDetailsPage from './pages/detail';
import PetDetailsNotFound from './pages/petDetailsNotFound';
import Root from './components/root';

// React Router v6 imports:
// - RouterProvider: The main component that provides routing context to the entire app
// - createBrowserRouter: Creates a router that uses the HTML5 history API (clean URLs)
// - createRoutesFromElements: Converts JSX Route elements into route objects
// - Route: Defines individual routes and what components they should render
import { RouterProvider, createBrowserRouter, createRoutesFromElements, Route } from 'react-router-dom';

// Create our application's router configuration
// This defines all the possible URLs and which components should render for each
const appRouter = createBrowserRouter(
  createRoutesFromElements(
    // Root route - this is the parent route that wraps all other routes
    // The Root component contains the navigation bar and an Outlet for child routes
    <Route path="/" element={<Root />}>

      {/* Index route - renders when the path is exactly "/" */}
      {/* Shows all pets on the homepage */}
      <Route index element={<HomePage />} />

      {/* Dynamic route with URL parameter ":type" */}
      {/* Matches URLs like "/dogs", "/cats", "/birds" */}
      {/* The :type parameter will be available via useParams() hook */}
      <Route path=":type" element={<HomePage />} />

      {/* Dynamic route with TWO URL parameters ":type" and ":id" */}
      {/* Matches URLs like "/dogs/123", "/cats/456" */}
      {/* Both parameters will be available via useParams() hook */}
      <Route path=":type/:id" element={<PetDetailsPage />} />

      {/* Static route for search results */}
      {/* Matches "/search" with query parameters like "/search?name=fluffy" */}
      <Route path="search" element={<SearchPage />} />

      {/* Static route for error page */}
      {/* Users are redirected here when pet details can't be found */}
      <Route path="pet-details-not-found" element={<PetDetailsNotFound />} />
    </Route>
  )
);

function App() {
  return (
    // RouterProvider makes routing available to the entire application
    // It takes the router configuration we created above
    // This replaces the old BrowserRouter component from earlier React Router versions
    <RouterProvider router={appRouter} />
  );
}

export default App;
