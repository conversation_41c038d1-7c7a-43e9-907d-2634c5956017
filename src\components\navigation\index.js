import React, { useEffect, useState } from 'react';
import { getPetTypes } from '../../api/petfinder';
import Logo from '../../assets/logo.svg';
import Search from '../search';
// NavLink is like Link but with additional features for navigation menus
// It can automatically apply CSS classes when the link is "active" (current page)
import { NavLink } from 'react-router-dom';

const Navigation = () => {
  const [petTypes, setPetTypes] = useState([]);

  useEffect(() => {
    async function getPetTypesData() {
      const { types } = await getPetTypes();
      setPetTypes(types);
    }

    getPetTypesData();
  }, []);

  return (
    <nav>
      <div className="nav-logo">
        <img src={Logo} alt="Petlover" />
        <Search />
      </div>
      <ul className="nav-links">
        <li key={'all'}>
          {/* NavLink for "All Pets" - goes to the root route "/" */}
          <NavLink
            to="/"  {/* 'to' prop replaces 'href' - tells React Router where to navigate */}
            {/* className can be a function that receives navigation state */}
            {/* isActive is true when this link matches the current URL */}
            className={({ isActive }) => isActive ? 'nav-link nav-link-active' : 'nav-link'}
          >
            All Pets
          </NavLink>
        </li>
        {petTypes
          ? petTypes.map((type) => (
              <li key={type.name}>
                {/* Dynamic NavLinks for each pet type (dogs, cats, etc.) */}
                <NavLink
                  {/* This creates URLs like "/dogs", "/cats" that match our ":type" route */}
                  to={`/${type._links.self.href.split('/').pop()}`}
                  key={type.name}
                  {/* Same active state logic - highlights the current pet type */}
                  className={({ isActive }) => isActive ? 'nav-link nav-link-active' : 'nav-link'}
                >
                  {type.name}s
                </NavLink>{' '}
              </li>
            ))
          : 'Loading...'}
      </ul>
    </nav>
  );
};

export default Navigation;
