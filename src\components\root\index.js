import React from 'react';
import Navigation from '../navigation';
// Outlet is a special React Router component that renders child routes
// Think of it as a "placeholder" where nested route components will appear
import { Outlet } from 'react-router-dom';

const Root = () => {
    return (
        <>
            {/* Navigation bar - this appears on every page */}
            <Navigation/>

            {/* Outlet component - this is WHERE nested routes will be rendered */}
            {/* When user visits "/", the HomePage component renders here */}
            {/* When user visits "/dogs", the HomePage component renders here (with type="dogs") */}
            {/* When user visits "/dogs/123", the PetDetailsPage component renders here */}
            {/* This is the key to nested routing - parent layout stays, content changes */}
            <Outlet />
        </>
    );
};

export default Root;