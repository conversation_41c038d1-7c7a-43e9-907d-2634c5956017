import React, { useRef } from 'react';
// createSearchParams: Utility to create URL search parameters (query strings)
// useNavigate: Hook that returns a function for programmatic navigation
import { createSearchParams, useNavigate } from 'react-router-dom';

const Search = () => {

  // useNavigate returns a function that can navigate to different routes
  // This is the imperative way to navigate (vs declarative with Link/NavLink)
  const navigate = useNavigate();

  const searchInputRef = useRef();

  const onSearchHandler = (e) => {
    e.preventDefault();

    // Create an object with our search parameters
    // This will become the query string in the URL
    const searchQuery = {
      name: searchInputRef.current.value
    }

    // createSearchParams converts our object into a URLSearchParams object
    // This handles URL encoding and proper formatting
    // Example: { name: "fluffy cat" } becomes "name=fluffy%20cat"
    const query = createSearchParams(searchQuery);

    // Use the navigate function to go to the search page with query parameters
    // This creates URLs like "/search?name=fluffy"
    // The SearchPage component will read these parameters with useSearchParams
    navigate(`/search?${query}`);
  };

  return (
    <form onSubmit={onSearchHandler} className="search-form">
      <input type="text" className="search" ref={searchInputRef} />
      <button type="submit" className="search-button">
        🔎
      </button>
    </form>
  );
};

export default Search;
