import React, { useEffect, useState } from 'react';
import { getPetDetails } from '../../api/petfinder';
import Hero from '../../components/hero';
// useParams: Extracts URL parameters (we need both :type and :id from the route)
// Navigate: Component for programmatic navigation (redirects)
import { useParams, Navigate } from 'react-router-dom';

const PetDetailsPage = () => {
  const [data, setData] = useState();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  // Extract the 'id' parameter from URLs like "/dogs/123"
  // We could also extract 'type' if needed: const { type, id } = useParams();
  const { id } = useParams();

  // Effect runs when component mounts and when 'id' parameter changes
  // This allows the same component to show different pets as user navigates
  useEffect(() => {
    async function getPetsData() {
      try {
        // Use the id from URL parameters to fetch specific pet details
        const petsData = await getPetDetails(id);
        setData(petsData);
        setError(false);
      } catch (e) {
        // If API call fails (pet not found), set error state
        // This will trigger a redirect to the error page
        setError(true);
      }
      setLoading(false);
    }

    getPetsData();
  }, [id]); // Re-run when the id parameter changes

  return (
    <div>
      {loading ? (
        <h3>Loading...</h3>
      ) : error ? (
        // Navigate component performs a programmatic redirect
        // When rendered, it immediately navigates to the specified route
        // This is React Router v6's way of handling redirects in components
        <Navigate to="/pet-details-not-found" />
      ) : (
        <main>
          <Hero
            image={data.photos[1]?.full || 'https://i.imgur.com/aEcJUFK.png'}
            displayText={`Meet ${data.name}`}
          />
          <div className="pet-detail">
            <div className="pet-image-container">
              <img
                className="pet-image"
                src={
                  data.photos[0]?.medium || 'https://i.imgur.com/aEcJUFK.png'
                }
                alt=""
              />
            </div>
            <div>
              <h1>{data.name}</h1>
              <h3>Breed: {data.breeds.primary}</h3>
              <p>Color: {data.colors.primary || 'Unknown'}</p>
              <p>Gender: {data.gender}</p>
              <h3>Description</h3>
              <p>{data.description}</p>
            </div>
          </div>
        </main>
      )}
    </div>
  );
};

export default PetDetailsPage;
