import React, { useEffect, useState } from 'react';
import { getPets } from '../../api/petfinder';
import <PERSON> from '../../components/hero';
// useParams: Hook that extracts URL parameters from the current route
// Link: Component for navigation that doesn't cause page refreshes
import { usePara<PERSON>, Link } from 'react-router-dom';

const HomePage = () => {
  const [data, setData] = useState(null);

  // useParams() returns an object with all URL parameters
  // For route ":type", this extracts the type value from URLs like "/dogs" or "/cats"
  // If we're on the index route ("/"), type will be undefined
  const { type } = useParams();

  // useEffect runs when the component mounts AND when 'type' changes
  // This means when user navigates from "/dogs" to "/cats", this will re-run
  useEffect(() => {
    async function getPetsData() {
      // Pass the type parameter to the API - undefined means "all pets"
      const petsData = await getPets(type);
      setData(petsData);
    }

    getPetsData();
  }, [type]); // Dependency array - effect runs when 'type' changes

  if (!data) {
    return <h2>Loading...</h2>;
  }

  return (
    <div className="page">
      <Hero />
      <h3>
        <span className="pet-type-label">{type ? `${type}s` : 'Pets'}</span>{' '}
        available for adoption near you
      </h3>

      {data.length ? (
        <div className="grid">
          {data.map((animal) => (
            {/* Link component replaces <a> tags for internal navigation */}
            {/* This prevents page refreshes and uses React Router's client-side routing */}
            <Link
              key={animal.id}
              {/* 'to' prop creates URLs like "/dogs/123" that match our ":type/:id" route */}
              {/* This will navigate to the PetDetailsPage component */}
              to={`/${animal.type.toLowerCase()}/${animal.id}`}
              className="pet"
            >
              <article>
                <div className="pet-image-container">
                  {
                    <img
                      className="pet-image"
                      src={
                        animal.photos[0]?.medium ||
                        '/missing-animal.png'
                      }
                      alt=""
                    />
                  }
                </div>
                <h3>{animal.name}</h3>
                <p>Breed: {animal.breeds.primary}</p>
                <p>Color: {animal.colors.primary}</p>
                <p>Gender: {animal.gender}</p>
              </article>
            </Link>
          ))}
        </div>
      ) : (
        <p className="prompt">No {type}s available for adoption now.</p>
      )}
    </div>
  );
};

export default HomePage;
