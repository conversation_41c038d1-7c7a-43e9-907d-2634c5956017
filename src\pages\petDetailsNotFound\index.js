import React from 'react';
// useNavigate: Hook for imperative navigation (programmatically changing routes)
import { useNavigate } from 'react-router-dom';

const PetDetailsNotFound = () => {

  // Get the navigate function for programmatic navigation
  // This is useful for navigation triggered by user actions (button clicks, form submissions)
  const navigate = useNavigate();

  const goHome = () => {
    // Navigate back to the home page ("/")
    // This is equivalent to clicking a Link with to="/"
    // navigate() can also go back in history: navigate(-1) or navigate(-2)
    navigate('/');
  }
  
  return (
    <main className="page">
      <h3>404: Who let the dogs out?</h3>
      <p>Sorry, but the details for this pet have not been uploaded by the shelter yet. Check back later!</p>
      <img
        src="https://i.chzbgr.com/full/8362031616/h9EB970C5/weve-lost-our-corgination"
        alt=""
      />
      <div className="actions-container">
        <button className="button" onClick={goHome}>
          Go Home
        </button>
      </div>
    </main>
  );
};

export default PetDetailsNotFound;
