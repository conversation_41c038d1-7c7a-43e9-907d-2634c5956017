import React, { useState, useEffect } from 'react';
import Hero from '../../components/hero';
import { getPets } from '../../api/petfinder';
import Pet from '../../components/pet';
// useSearchParams: Hook for reading and writing URL search parameters (query strings)
import { useSearchParams } from 'react-router-dom';

const SearchPage = () => {

  // useSearchParams returns an array with [searchParams, setSearchParams]
  // searchParams is like URLSearchParams - it has methods to read query parameters
  // setSearchParams can be used to update the URL query parameters
  const [searchParams] = useSearchParams();

  // Extract the 'name' parameter from URLs like "/search?name=fluffy"
  // searchParams.get('name') returns the value or null if not present
  const petNameToFind = searchParams.get('name');

  const [pets, setPets] = useState([]);

  // Effect runs when component mounts and when search query changes
  // This allows real-time search results as the URL parameters change
  useEffect(() => {
    async function getPetsData() {
      // Call API with empty type (all types) and the search name
      // This searches across all pet types for the given name
      const petsData = await getPets('', petNameToFind);

      setPets(petsData);
    }

    getPetsData();
  }, [petNameToFind]); // Re-run when search query changes

  return (
    <div className="page">
      <Hero displayText={`Results for ${petNameToFind}`} />

      <h3>Pets available for adoption near you</h3>

      <main>
        <div className="grid">
          {pets.map((pet) => (
            <Pet animal={pet} key={pet.id} />
          ))}
        </div>
      </main>
    </div>
  );
};

export default SearchPage;
